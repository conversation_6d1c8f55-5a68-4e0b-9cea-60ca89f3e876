import { Link } from 'react-router-dom';
import { Leaf, Heart, Users, BookOpen, ChefHat, BarChart3, Star, ArrowRight, CheckCircle, Menu, X, Award, Shield, HelpCircle, Play } from 'lucide-react';
import { useState } from 'react';

const Landing = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const testimonials = [
    {
      name: "<PERSON> Sa<PERSON>",
      role: "Ibu dari 2 anak",
      location: "Kupang, NTT",
      quote: "Sejak menggunakan GiziLokal, saya lebih paham cara mengolah daun kelor dan jagung lokal untuk anak-anak. Berat badan si kecil naik signifikan!",
      avatar: "MS"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Kader Posyandu",
      location: "Ende, NTT",
      quote: "Platform ini sangat membantu dalam mengedukasi orang tua. Data pertumbuhan anak menjadi lebih mudah dipantau dan dipahami.",
      avatar: "Y<PERSON>"
    },
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON><PERSON>",
      location: "Flores, NTT",
      quote: "Resep-resep dengan bahan lokal sangat praktis dan bergizi. Anak saya sekarang lebih suka makan dan tinggi badannya normal.",
      avatar: "ED"
    }
  ];

  const faqs = [
    {
      question: "Apakah aplikasi ini benar-benar gratis?",
      answer: "Ya, GiziLokal 100% gratis selamanya. Kami berkomitmen untuk memberikan akses edukasi gizi terbaik untuk semua keluarga di NTT tanpa biaya apapun."
    },
    {
      question: "Bagaimana cara memulai menggunakan GiziLokal?",
      answer: "Cukup daftar dengan email atau nomor HP, lengkapi profil anak Anda, dan mulai jelajahi fitur-fitur yang tersedia. Tim kami juga menyediakan panduan lengkap untuk pengguna baru."
    },
    {
      question: "Apakah data pertumbuhan anak saya aman?",
      answer: "Keamanan data adalah prioritas utama kami. Semua informasi disimpan dengan enkripsi tingkat tinggi dan hanya dapat diakses oleh Anda. Kami tidak membagikan data pribadi kepada pihak ketiga."
    },
    {
      question: "Bahan makanan lokal apa saja yang direkomendasikan?",
      answer: "Kami fokus pada bahan lokal NTT seperti daun kelor, jagung, ubi jalar, ikan laut, telur ayam kampung, dan berbagai sayuran tradisional yang kaya nutrisi dan mudah didapat."
    },
    {
      question: "Bagaimana jika saya menemukan tanda-tanda stunting pada anak?",
      answer: "Aplikasi akan memberikan peringatan dini dan rekomendasi langkah-langkah yang perlu diambil. Kami juga menyediakan informasi untuk konsultasi dengan tenaga kesehatan terdekat."
    }
  ];

  const steps = [
    {
      number: "01",
      title: "Daftar & Profil Anak",
      description: "Buat akun gratis dan masukkan data dasar anak seperti umur, tinggi, dan berat badan"
    },
    {
      number: "02", 
      title: "Pantau Pertumbuhan",
      description: "Input data berkala dan lihat perkembangan anak melalui grafik WHO yang mudah dipahami"
    },
    {
      number: "03",
      title: "Ikuti Rekomendasi",
      description: "Dapatkan resep makanan bergizi dan tips parenting sesuai kondisi anak Anda"
    },
    {
      number: "04",
      title: "Komunitas & Support",
      description: "Bergabung dengan komunitas orang tua dan dapatkan dukungan dari kader posyandu"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-100 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center shadow-sm">
                <Leaf className="w-7 h-7 text-white" />
              </div>
              <span className="text-3xl font-bold text-emerald-600">
                GiziLokal
              </span>
            </div>
            
            <div className="hidden md:flex items-center space-x-6">
              <Link
                to="/login"
                className="text-gray-600 font-medium hover:text-emerald-600 transition-colors px-6 py-3 rounded-lg hover:bg-emerald-50"
              >
                Masuk
              </Link>
              <Link
                to="/register"
                className="bg-emerald-500 text-white px-8 py-3 rounded-lg font-semibold hover:bg-emerald-600 transition-colors shadow-sm"
              >
                Daftar Gratis
              </Link>
            </div>

            <button 
              className="md:hidden p-2 text-gray-600"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-100 px-4 py-6 space-y-4 shadow-sm">
            <Link
              to="/login"
              className="block text-gray-600 font-medium py-3"
              onClick={() => setMobileMenuOpen(false)}
            >
              Masuk
            </Link>
            <Link
              to="/register"
              className="block bg-emerald-500 text-white px-6 py-3 rounded-lg font-semibold text-center hover:bg-emerald-600 transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Daftar Gratis
            </Link>
          </div>
        )}
      </header>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-emerald-50/50 via-white to-teal-50/30">
        {/* Subtle Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-64 h-64 bg-emerald-100/40 rounded-full filter blur-3xl opacity-50"></div>
          <div className="absolute top-40 right-10 w-64 h-64 bg-teal-100/40 rounded-full filter blur-3xl opacity-50"></div>
          <div className="absolute bottom-20 left-1/2 w-64 h-64 bg-cyan-100/30 rounded-full filter blur-3xl opacity-40"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="text-center lg:text-left space-y-8">
              {/* Badge */}
              <div className="inline-flex items-center space-x-2 bg-emerald-50 px-6 py-3 rounded-full text-sm font-medium text-emerald-700 border border-emerald-100">
                <Star className="w-5 h-5 text-amber-500" />
                <span>Platform #1 Cegah Stunting di NTT</span>
              </div>
              
              {/* Main Heading */}
              <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-gray-800 leading-tight">
                Cegah{' '}
                <span className="text-emerald-600">
                  Stunting
                </span>
                <br />
                dengan{' '}
                <span className="text-amber-500">
                  Gizi Lokal
                </span>
              </h1>
              
              {/* Subtitle */}
              <p className="text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-2xl">
                Platform edukasi gizi terdepan untuk orang tua dan kader posyandu di NTT. 
                Pantau tumbuh kembang anak dengan resep makanan lokal bergizi tinggi.
              </p>
              
              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link 
                  to="/register" 
                  className="group bg-emerald-500 text-white px-10 py-4 rounded-xl font-semibold text-lg hover:bg-emerald-600 transition-colors flex items-center justify-center space-x-3 shadow-sm"
                >
                  <span>Mulai Sekarang</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link 
                  to="/login" 
                  className="bg-white text-gray-700 border border-gray-200 px-10 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-colors text-center shadow-sm"
                >
                  Sudah Punya Akun?
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-emerald-500" />
                  <span className="font-medium">Gratis Selamanya</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-emerald-500" />
                  <span className="font-medium">Standar WHO</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-emerald-500" />
                  <span className="font-medium">Bahan Lokal NTT</span>
                </div>
              </div>
            </div>
            
            {/* Hero Image */}
            <div className="relative lg:mt-0 mt-16">
              <div className="relative w-full max-w-lg mx-auto">
                <div className="aspect-square bg-gradient-to-br from-emerald-100 via-white to-teal-100 rounded-3xl shadow-lg p-8">
                  <div className="w-full h-full bg-emerald-500 rounded-2xl flex items-center justify-center relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                    <Heart className="w-24 h-24 text-white" />
                    <div className="absolute top-6 right-6 w-3 h-3 bg-amber-400 rounded-full"></div>
                    <div className="absolute bottom-6 left-6 w-2 h-2 bg-cyan-400 rounded-full"></div>
                  </div>
                </div>
                
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 w-16 h-16 bg-amber-400 rounded-xl flex items-center justify-center shadow-sm">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -bottom-4 -left-4 w-14 h-14 bg-cyan-400 rounded-lg flex items-center justify-center shadow-sm">
                  <Leaf className="w-7 h-7 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Cara{' '}
              <span className="text-emerald-600">
                Kerja
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Hanya 4 langkah mudah untuk memulai perjalanan gizi sehat anak Anda
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative text-center">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold text-emerald-600">
                  {step.number}
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">{step.title}</h3>
                <p className="text-gray-600 leading-relaxed">{step.description}</p>
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-full w-full">
                    <ArrowRight className="w-6 h-6 text-emerald-300 mx-auto" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gray-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Fitur{' '}
              <span className="text-emerald-600">
                Unggulan
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Solusi lengkap dan modern untuk mencegah stunting dengan memanfaatkan kekayaan bahan makanan lokal NTT
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
            {/* Feature Cards */}
            <div className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-sm">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 text-center">Pantau Tumbuh Kembang</h3>
              <p className="text-gray-600 text-center leading-relaxed">
                Lacak pertumbuhan anak dengan grafik interaktif berdasarkan standar WHO yang akurat dan mudah dipahami
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-sm">
                <ChefHat className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 text-center">Resep Makanan Lokal</h3>
              <p className="text-gray-600 text-center leading-relaxed">
                Koleksi resep bergizi menggunakan bahan lokal NTT seperti kelor, sorgum, dan ikan segar
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-sm">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 text-center">Edukasi Gizi</h3>
              <p className="text-gray-600 text-center leading-relaxed">
                Artikel dan panduan lengkap tentang nutrisi, stunting, dan kesehatan anak yang mudah dipahami
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="w-16 h-16 bg-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-sm">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 text-center">Untuk Kader Posyandu</h3>
              <p className="text-gray-600 text-center leading-relaxed">
                Tools khusus untuk kader dalam mengelola data dan komunikasi yang efektif dengan orang tua
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-sm">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 text-center">Deteksi Dini Stunting</h3>
              <p className="text-gray-600 text-center leading-relaxed">
                Sistem peringatan otomatis untuk mengidentifikasi risiko stunting pada anak sejak dini
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-sm">
                <Leaf className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 text-center">Bahan Lokal NTT</h3>
              <p className="text-gray-600 text-center leading-relaxed">
                Fokus pada pemanfaatan superfood lokal yang mudah didapat dan terjangkau di NTT
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-emerald-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Kata{' '}
              <span className="text-emerald-600">
                Mereka
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Dengarkan pengalaman nyata dari keluarga di NTT yang telah merasakan manfaat GiziLokal
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{testimonial.name}</h4>
                    <p className="text-sm text-gray-500">{testimonial.role}</p>
                    <p className="text-sm text-emerald-600">{testimonial.location}</p>
                  </div>
                </div>
                <p className="text-gray-600 leading-relaxed italic">"{testimonial.quote}"</p>
                <div className="flex mt-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-amber-400 fill-current" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-24 bg-emerald-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/5"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Bersama Melawan Stunting di NTT
            </h2>
            <p className="text-xl text-emerald-100 max-w-3xl mx-auto leading-relaxed">
              Mari bergabung dalam misi mulia mencegah stunting dengan memanfaatkan kekayaan gizi lokal yang melimpah
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 text-center">
            <div className="group">
              <div className="text-5xl lg:text-6xl font-bold mb-4 text-amber-200">
                27.67%
              </div>
              <p className="text-emerald-100 text-lg font-medium">Prevalensi Stunting di NTT (2022)</p>
            </div>
            <div className="group">
              <div className="text-5xl lg:text-6xl font-bold mb-4 text-amber-200">
                1000
              </div>
              <p className="text-emerald-100 text-lg font-medium">Hari Pertama Kehidupan Kritis</p>
            </div>
            <div className="group">
              <div className="text-5xl lg:text-6xl font-bold mb-4 text-amber-200">
                100%
              </div>
              <p className="text-emerald-100 text-lg font-medium">Dapat Dicegah dengan Gizi Tepat</p>
            </div>
          </div>
        </div>
      </section>

      {/* Partnership & Trust Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Dipercaya{' '}
              <span className="text-emerald-600">
                & Didukung
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Bekerjasama dengan institusi terpercaya untuk memberikan informasi gizi terbaik
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="flex flex-col items-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Award className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Standar WHO</h3>
              <p className="text-gray-600">Menggunakan kurva pertumbuhan dan standar gizi dari World Health Organization</p>
            </div>

            <div className="flex flex-col items-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Kemenkes RI</h3>
              <p className="text-gray-600">Mengacu pada panduan gizi dan pencegahan stunting dari Kementerian Kesehatan</p>
            </div>

            <div className="flex flex-col items-center p-6">
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-4">
                <Users className="w-8 h-8 text-emerald-600" />
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Posyandu NTT</h3>
              <p className="text-gray-600">Berkolaborasi dengan kader posyandu di seluruh wilayah NTT</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Pertanyaan{' '}
              <span className="text-emerald-600">
                Umum
              </span>
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              Jawaban untuk pertanyaan yang sering diajukan tentang GiziLokal
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-100">
                <button
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                >
                  <span className="font-medium text-gray-800">{faq.question}</span>
                  <HelpCircle className={`w-5 h-5 text-emerald-600 transition-transform ${openFAQ === index ? 'rotate-180' : ''}`} />
                </button>
                {openFAQ === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-48 h-48 bg-emerald-100/30 rounded-full filter blur-2xl"></div>
          <div className="absolute bottom-20 left-20 w-48 h-48 bg-teal-100/30 rounded-full filter blur-2xl"></div>
        </div>
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6 leading-tight">
            Mulai Perjalanan{' '}
            <span className="text-emerald-600">
              Gizi Sehat
            </span>{' '}
            Hari Ini
          </h2>
          <p className="text-xl text-gray-600 mb-10 max-w-2xl mx-auto leading-relaxed">
            Bergabunglah dengan komunitas orang tua dan kader posyandu yang peduli dengan masa depan cerah anak-anak NTT
          </p>
          <Link 
            to="/register" 
            className="group inline-flex items-center space-x-3 bg-emerald-500 text-white px-10 py-5 rounded-2xl font-semibold text-xl hover:bg-emerald-600 transition-colors shadow-sm"
          >
            <span>Daftar Gratis Sekarang</span>
            <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center">
                  <Leaf className="w-7 h-7 text-white" />
                </div>
                <span className="text-3xl font-bold text-emerald-400">
                  GiziLokal
                </span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md leading-relaxed">
                Platform edukasi gizi terdepan untuk mencegah stunting dengan memanfaatkan bahan makanan lokal NTT yang bergizi tinggi.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-lg mb-4 text-white">Fitur Utama</h4>
              <ul className="space-y-3 text-gray-300">
                <li className="hover:text-emerald-400 transition-colors cursor-pointer">Pantau Tumbuh Kembang</li>
                <li className="hover:text-emerald-400 transition-colors cursor-pointer">Resep Makanan Lokal</li>
                <li className="hover:text-emerald-400 transition-colors cursor-pointer">Edukasi Gizi</li>
                <li className="hover:text-emerald-400 transition-colors cursor-pointer">Tools Kader Posyandu</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-lg mb-4 text-white">Kontak</h4>
              <div className="space-y-3 text-gray-300">
                <p className="hover:text-emerald-400 transition-colors">
                  📧 <EMAIL>
                </p>
                <p className="hover:text-emerald-400 transition-colors">
                  📱 +62 xxx xxxx xxxx
                </p>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-700 pt-8 text-center">
            <p className="text-gray-400 text-lg">
              &copy; 2024 GiziLokal. Semua hak dilindungi. Made with ❤️ for NTT children.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
