
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface User {
  id: string;
  email: string;
  full_name: string;
  role: 'parent' | 'cadre';
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, fullName: string, role: 'parent' | 'cadre') => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Check for existing session
    const storedUser = localStorage.getItem('gizi_user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    // Mock login - replace with Supabase auth
    console.log('Login attempt:', { email, password });
    
    // Mock user data
    const mockUser: User = {
      id: '1',
      email,
      full_name: email.includes('kader') ? 'Kader Posyandu' : 'Ibu Sarah',
      role: email.includes('kader') ? 'cadre' : 'parent'
    };
    
    setUser(mockUser);
    localStorage.setItem('gizi_user', JSON.stringify(mockUser));
  };

  const register = async (email: string, password: string, fullName: string, role: 'parent' | 'cadre') => {
    // Mock registration - replace with Supabase auth
    console.log('Register attempt:', { email, password, fullName, role });
    
    const mockUser: User = {
      id: '1',
      email,
      full_name: fullName,
      role
    };
    
    setUser(mockUser);
    localStorage.setItem('gizi_user', JSON.stringify(mockUser));
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('gizi_user');
    navigate('/');
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
