
import { useAuth } from '../contexts/AuthContext';
import { LogOut, Bell, Menu, Home, Users, BookOpen, ChefHat, Database, Send } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';
import { useState } from 'react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title: string;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children, title }) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigation = [
    { name: 'Beranda', href: '/dashboard', icon: Home },
    ...(user?.role === 'parent' ? [
      { name: '<PERSON><PERSON>', href: '/children', icon: Users },
      { name: 'Resep Lokal', href: '/recipes', icon: ChefHat },
      { name: '<PERSON><PERSON><PERSON>', href: '/education', icon: BookOpen },
    ] : [
      { name: 'Kelola Data', href: '/manage-data', icon: Database },
      { name: '<PERSON><PERSON> Info', href: '/send-info', icon: Send },
      { name: 'Edukasi', href: '/education', icon: BookOpen },
    ])
  ];

  return (
    <div className="min-h-screen bg-gizi-light">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Brand */}
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gizi-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">G</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gizi-dark">GiziLokal</h1>
                <p className="text-xs text-gray-500 hidden sm:block">
                  {user?.role === 'cadre' ? 'Kader Posyandu' : 'Orang Tua'}
                </p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-1">
              {navigation.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-gizi-primary text-white'
                        : 'text-gray-600 hover:text-gizi-primary hover:bg-gizi-primary/10'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
            </nav>

            {/* User Actions */}
            <div className="flex items-center space-x-2">
              <span className="hidden sm:block text-sm text-gray-600">
                {user?.full_name}
              </span>
              <button className="p-2 text-gray-500 hover:text-gizi-primary transition-colors">
                <Bell className="w-5 h-5" />
              </button>
              <button
                onClick={logout}
                className="p-2 text-gray-500 hover:text-red-500 transition-colors"
                title="Keluar"
              >
                <LogOut className="w-5 h-5" />
              </button>
              
              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 text-gray-500 hover:text-gizi-primary transition-colors"
              >
                <Menu className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t border-gray-200 py-2">
              <nav className="space-y-1">
                {navigation.map((item) => {
                  const Icon = item.icon;
                  const isActive = location.pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        isActive
                          ? 'bg-gizi-primary text-white'
                          : 'text-gray-600 hover:text-gizi-primary hover:bg-gizi-primary/10'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.name}</span>
                    </Link>
                  );
                })}
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <h1 className="text-2xl font-bold text-gizi-dark">{title}</h1>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {children}
      </main>
    </div>
  );
};

export default DashboardLayout;
