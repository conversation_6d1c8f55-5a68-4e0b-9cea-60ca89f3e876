
# GiziLokal - Progressive Web App untuk Pencegahan Stunting

![GiziLokal Logo](https://img.shields.io/badge/GiziLokal-Cegah%20Stunting-4CAF50?style=for-the-badge&logo=leaf)

## 🌟 Tentang GiziLokal

GiziLokal adalah Progressive Web App (PWA) yang dirancang khusus untuk membantu orang tua dan kader Posyandu di Nusa Tenggara Timur (NTT) dalam mencegah stunting dengan memanfaatkan bahan makanan lokal yang bergizi tinggi.

### 🎯 Misi Utama
Memberdayakan masyarakat NTT dalam pencegahan stunting melalui:
- Pemantauan pertumbuhan anak yang mudah dan akurat
- Edukasi gizi dengan pemanfaatan pangan lokal
- Platform digital yang dapat diakses offline
- Interface yang ramah untuk berbagai tingkat literasi digital

## 🚀 Fitur Utama

### 👨‍👩‍👧‍👦 Untuk Orang Tua
- **Pantau Tumbuh**: Grafik pertumbuhan dengan standar WHO
- **Resep Lokal**: <PERSON>leks<PERSON> resep MP-ASI dari bahan lokal NTT
- **Edukasi Gizi**: Artikel dan tips pencegahan stunting
- **Riwayat Pertumbuhan**: Tracking data berat dan tinggi badan anak

### 👥 Untuk Kader Posyandu
- **Kelola Data Massal**: Input data pertumbuhan multiple anak
- **Monitoring Risiko**: Identifikasi anak berisiko stunting
- **Kirim Informasi**: Broadcast pesan edukasi ke orang tua
- **Dashboard Analytics**: Overview status gizi wilayah

### 📱 Fitur PWA
- **Offline Access**: Bekerja tanpa koneksi internet
- **Installable**: Dapat diinstall di smartphone
- **Fast Loading**: Optimized untuk koneksi lambat
- **Responsive**: Desain mobile-first yang adaptif

## 🛠 Tech Stack

### Frontend
- **React 18** - UI Library dengan hooks modern
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Fast build tool dan dev server

### Backend & Database
- **Supabase** - Backend-as-a-Service
  - PostgreSQL database
  - Real-time subscriptions
  - Auto-generated APIs
  - Authentication system

### PWA & Performance
- **Vite PWA Plugin** - Service Worker generation
- **Workbox** - Offline caching strategies
- **React Query** - Data fetching & caching

### UI Components
- **shadcn/ui** - Pre-built accessible components
- **Lucide React** - Modern icon library
- **Chart.js** - Data visualization (untuk implementasi chart)

## 🎨 Design System

### Color Palette
```css
/* Primary Colors */
--gizi-primary: #4CAF50    /* Healthy green - growth & wellness */
--gizi-secondary: #FFC107  /* Warm yellow - nutrition & hope */
--gizi-danger: #EF4444     /* Alert red - risk notifications */

/* Neutral Colors */
--gizi-light: #F7FAFC      /* Background gray */
--gizi-dark: #4A5568       /* Text gray */
```

### Typography
- **Font Family**: Poppins (Google Fonts)
- **Hierarchy**: Clear heading levels dengan readable font sizes
- **Minimum Text Size**: 16px untuk body text (accessibility)

### Component System
- **Card-based Layout**: Clean information grouping
- **Consistent Spacing**: 4px grid system
- **Accessible Colors**: WCAG compliant contrast ratios

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ dan npm
- Git

### Local Development

1. **Clone Repository**
```bash
git clone <repository-url>
cd gizilokal
```

2. **Install Dependencies**
```bash
npm install
```

3. **Environment Setup**
```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
# Add your Supabase credentials
```

4. **Start Development Server**
```bash
npm run dev
```

Aplikasi akan berjalan di `http://localhost:5173`

### Build for Production

```bash
# Build aplikasi
npm run build

# Preview build
npm run preview
```

## 🗃 Database Schema

### Tabel Utama

#### `profiles`
```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  role role_type NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `children`
```sql
CREATE TABLE children (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  parent_id UUID REFERENCES profiles(id),
  name TEXT NOT NULL,
  birth_date DATE NOT NULL,
  gender gender_type NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `growth_records`
```sql
CREATE TABLE growth_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  child_id UUID REFERENCES children(id),
  weight_kg DECIMAL(5,2) NOT NULL,
  height_cm DECIMAL(5,2) NOT NULL,
  record_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Data Statis

#### Resep Lokal (`src/data/recipes.json`)
- 6+ resep MP-ASI dari bahan lokal NTT
- Kelor, ubi jalar ungu, ikan cakalang, sorgum
- Nutritional benefits dan cooking instructions

#### Artikel Edukasi (`src/data/articles.json`)
- Konten stunting prevention
- Tips gizi dan tumbuh kembang
- Pemanfaatan pangan lokal

## 🔒 Authentication & Security

### User Roles
- **Parent**: Akses individual untuk anak sendiri
- **Cadre**: Akses untuk mengelola data komunitas

### Data Security
- Row Level Security (RLS) policies
- Role-based access control
- Secure data transmission (HTTPS)

## 📱 PWA Configuration

### Manifest (`public/manifest.json`)
```json
{
  "name": "GiziLokal",
  "short_name": "GiziLokal",
  "description": "Cegah Stunting dengan Gizi Lokal",
  "theme_color": "#4CAF50",
  "background_color": "#F7FAFC",
  "display": "standalone",
  "start_url": "/",
  "scope": "/"
}
```

### Service Worker Strategy
- **App Shell**: Cache static assets
- **Runtime Caching**: Dynamic content
- **Offline Fallbacks**: Essential functionality

## 🧪 Testing

### Unit Testing
```bash
npm run test
```

### E2E Testing
```bash
npm run test:e2e
```

### Accessibility Testing
- Lighthouse audits
- WAVE accessibility testing
- Keyboard navigation testing

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Connect repository to Vercel
# Configure environment variables
# Deploy automatically on push
```

### Netlify
```bash
# Build command: npm run build
# Publish directory: dist
# Environment variables: Set in Netlify dashboard
```

### Manual Deployment
```bash
npm run build
# Upload dist/ folder to hosting provider
```

## 📊 Performance

### Core Web Vitals Targets
- **LCP** (Largest Contentful Paint): < 2.5s
- **FID** (First Input Delay): < 100ms
- **CLS** (Cumulative Layout Shift): < 0.1

### Optimization Strategies
- Code splitting dengan React.lazy
- Image optimization dan lazy loading
- Service Worker caching
- Bundle size optimization

## 🤝 Contributing

### Development Workflow
1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Code Standards
- TypeScript strict mode
- ESLint + Prettier configuration
- Conventional commit messages
- Component documentation

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 👥 Team

- **Product Owner**: Tim Kesehatan NTT
- **Lead Developer**: [Your Name]
- **UI/UX Designer**: [Designer Name]
- **Health Consultant**: [Expert Name]

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: https://docs.gizilokal.id
- **Issues**: GitHub Issues page

## 🙏 Acknowledgments

- WHO Growth Charts Standards
- Kementerian Kesehatan RI
- Dinas Kesehatan NTT
- Posyandu Network NTT
- Open source community

---

**GiziLokal** - Bersama mencegah stunting dengan kearifan pangan lokal NTT 🌿
