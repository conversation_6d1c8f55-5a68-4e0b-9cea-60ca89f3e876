
import { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import { Search, Clock, Baby, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import recipesData from '../data/recipes.json';

const Recipes = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterIngredient, setFilterIngredient] = useState('all');
  const [filterAge, setFilterAge] = useState('all');

  const ingredients = ['Kelor', 'Ubi Jalar', 'Ikan', 'Sorgum', 'Labu', 'Tahu'];
  const ageRanges = ['6+ bulan', '6-12 bulan', '8+ bulan'];

  const filteredRecipes = useMemo(() => {
    return recipesData.filter(recipe => {
      const matchesSearch = recipe.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           recipe.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesIngredient = filterIngredient === 'all' || recipe.main_ingredient === filterIngredient;
      const matchesAge = filterAge === 'all' || recipe.age_range === filterAge;
      
      return matchesSearch && matchesIngredient && matchesAge;
    });
  }, [searchTerm, filterIngredient, filterAge]);

  const clearFilters = () => {
    setSearchTerm('');
    setFilterIngredient('all');
    setFilterAge('all');
  };

  return (
    <DashboardLayout title="Resep Lokal">
      <div className="space-y-6">
        {/* Search and Filters */}
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Cari resep..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filters */}
          <div className="flex space-x-3">
            <Select value={filterIngredient} onValueChange={setFilterIngredient}>
              <SelectTrigger className="flex-1">
                <div className="flex items-center space-x-2">
                  <Filter className="w-4 h-4" />
                  <SelectValue placeholder="Bahan Utama" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Bahan</SelectItem>
                {ingredients.map(ingredient => (
                  <SelectItem key={ingredient} value={ingredient}>{ingredient}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterAge} onValueChange={setFilterAge}>
              <SelectTrigger className="flex-1">
                <div className="flex items-center space-x-2">
                  <Baby className="w-4 h-4" />
                  <SelectValue placeholder="Usia" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Usia</SelectItem>
                {ageRanges.map(age => (
                  <SelectItem key={age} value={age}>{age}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Active Filters */}
          {(filterIngredient !== 'all' || filterAge !== 'all' || searchTerm) && (
            <div className="flex items-center justify-between bg-gizi-light rounded-lg p-3">
              <span className="text-sm text-gray-600">
                {filteredRecipes.length} resep ditemukan
              </span>
              <button
                onClick={clearFilters}
                className="text-sm text-gizi-primary hover:underline"
              >
                Hapus Filter
              </button>
            </div>
          )}
        </div>

        {/* Recipes Grid */}
        <div className="grid gap-4">
          {filteredRecipes.map((recipe) => (
            <Link
              key={recipe.id}
              to={`/recipes/${recipe.id}`}
              className="gizi-card hover:shadow-md transition-shadow"
            >
              <div className="flex space-x-4">
                {/* Recipe Image */}
                <div className="w-20 h-20 bg-gradient-to-br from-gizi-primary/20 to-gizi-secondary/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Baby className="w-8 h-8 text-gizi-primary" />
                </div>

                {/* Recipe Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gizi-dark mb-1 truncate">
                    {recipe.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {recipe.description}
                  </p>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{recipe.prep_time}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Baby className="w-3 h-3" />
                      <span>{recipe.age_range}</span>
                    </div>
                  </div>
                  
                  <div className="mt-2">
                    <span className="inline-block bg-gizi-primary/10 text-gizi-primary text-xs px-2 py-1 rounded-full">
                      {recipe.main_ingredient}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Empty State */}
        {filteredRecipes.length === 0 && (
          <div className="text-center py-12">
            <Baby className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">Resep tidak ditemukan</h3>
            <p className="text-gray-400 mb-4">Coba ubah filter atau kata kunci pencarian</p>
            <button
              onClick={clearFilters}
              className="text-gizi-primary hover:underline"
            >
              Hapus semua filter
            </button>
          </div>
        )}

        {/* Tip Card */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
          <h3 className="font-medium text-yellow-800 mb-1">💡 Tips Pemberian MP-ASI</h3>
          <p className="text-sm text-yellow-700">
            Selalu konsultasikan dengan tenaga kesehatan sebelum memberikan makanan baru. 
            Berikan ASI eksklusif hingga 6 bulan, lalu mulai MP-ASI bertahap.
          </p>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Recipes;
