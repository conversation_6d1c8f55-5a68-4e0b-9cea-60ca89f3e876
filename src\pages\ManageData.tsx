
import DashboardLayout from '../components/DashboardLayout';
import { useState } from 'react';
import { Search, Plus, AlertTriangle, Users, TrendingUp } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';

// Mock data for cadre view
const mockChildrenData = [
  { id: '1', name: '<PERSON><PERSON><PERSON>', age: '2 tahun 3 bulan', parent: '<PERSON><PERSON> <PERSON>', weight: 12.5, height: 85.2, status: 'normal' },
  { id: '2', name: '<PERSON><PERSON>', age: '3 tahun 1 bulan', parent: '<PERSON><PERSON>', weight: 13.2, height: 90.1, status: 'warning' },
  { id: '3', name: '<PERSON><PERSON>', age: '1 tahun 8 bulan', parent: '<PERSON><PERSON>', weight: 9.8, height: 78.5, status: 'danger' },
  { id: '4', name: '<PERSON>', age: '2 tahun 6 bulan', parent: '<PERSON><PERSON> <PERSON>', weight: 12.8, height: 86.0, status: 'normal' },
  { id: '5', name: 'Putri Ayu', age: '1 tahun 5 bulan', parent: 'Ibu Maya', weight: 9.2, height: 76.8, status: 'warning' },
];

const ManageData = () => {
  const [searchTerm, set<PERSON>earchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedChild, setSelectedChild] = useState('');
  const [bulkData, setBulkData] = useState({
    weight: '',
    height: '',
    date: new Date().toISOString().split('T')[0]
  });

  const filteredChildren = mockChildrenData.filter(child => {
    const matchesSearch = child.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         child.parent.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || child.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'danger': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal': return 'Normal';
      case 'warning': return 'Perhatian';
      case 'danger': return 'Berisiko';
      default: return 'Unknown';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal': return <TrendingUp className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'danger': return <AlertTriangle className="w-4 h-4" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  const handleBulkSubmit = () => {
    if (!selectedChild || !bulkData.weight || !bulkData.height) {
      toast({
        title: "Data tidak lengkap",
        description: "Pilih anak dan isi semua data",
        variant: "destructive",
      });
      return;
    }

    console.log('Bulk data entry:', { selectedChild, ...bulkData });
    toast({
      title: "Data berhasil disimpan",
      description: "Data pertumbuhan telah ditambahkan",
    });

    // Reset form
    setSelectedChild('');
    setBulkData({
      weight: '',
      height: '',
      date: new Date().toISOString().split('T')[0]
    });
  };

  const statusCounts = {
    total: mockChildrenData.length,
    normal: mockChildrenData.filter(c => c.status === 'normal').length,
    warning: mockChildrenData.filter(c => c.status === 'warning').length,
    danger: mockChildrenData.filter(c => c.status === 'danger').length,
  };

  return (
    <DashboardLayout title="Kelola Data Warga">
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-2 gap-4">
          <div className="gizi-card text-center">
            <div className="text-2xl font-bold text-gizi-primary mb-1">{statusCounts.total}</div>
            <div className="text-sm text-gray-600">Total Anak</div>
          </div>
          <div className="gizi-card text-center">
            <div className="text-2xl font-bold text-red-500 mb-1">{statusCounts.danger}</div>
            <div className="text-sm text-gray-600">Berisiko</div>
          </div>
        </div>

        {/* Quick Input Data */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4 flex items-center">
            <Plus className="w-5 h-5 mr-2" />
            Input Data Cepat
          </h2>
          
          <div className="space-y-4">
            <Select value={selectedChild} onValueChange={setSelectedChild}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih anak..." />
              </SelectTrigger>
              <SelectContent>
                {mockChildrenData.map(child => (
                  <SelectItem key={child.id} value={child.id}>
                    {child.name} ({child.parent})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="grid grid-cols-3 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tanggal
                </label>
                <Input
                  type="date"
                  value={bulkData.date}
                  onChange={(e) => setBulkData(prev => ({ ...prev, date: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  BB (kg)
                </label>
                <Input
                  type="number"
                  step="0.1"
                  placeholder="12.5"
                  value={bulkData.weight}
                  onChange={(e) => setBulkData(prev => ({ ...prev, weight: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  TB (cm)
                </label>
                <Input
                  type="number"
                  step="0.1"
                  placeholder="85.2"
                  value={bulkData.height}
                  onChange={(e) => setBulkData(prev => ({ ...prev, height: e.target.value }))}
                />
              </div>
            </div>

            <Button onClick={handleBulkSubmit} className="w-full gizi-button-primary">
              Simpan Data
            </Button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Cari nama anak atau orang tua..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Filter status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Status</SelectItem>
              <SelectItem value="normal">Normal</SelectItem>
              <SelectItem value="warning">Perlu Perhatian</SelectItem>
              <SelectItem value="danger">Berisiko Stunting</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Children List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gizi-dark">
              Data Anak ({filteredChildren.length})
            </h2>
          </div>

          {filteredChildren.map((child) => (
            <div key={child.id} className="gizi-card">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-gizi-dark">{child.name}</h3>
                  <p className="text-sm text-gray-600">{child.age} • {child.parent}</p>
                </div>
                <div className={`px-3 py-1 rounded-full border text-sm font-medium flex items-center space-x-1 ${getStatusColor(child.status)}`}>
                  {getStatusIcon(child.status)}
                  <span>{getStatusText(child.status)}</span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="bg-gizi-light rounded-lg p-3">
                  <div className="text-gray-600">Berat Badan</div>
                  <div className="font-semibold text-gizi-dark">{child.weight} kg</div>
                </div>
                <div className="bg-gizi-light rounded-lg p-3">
                  <div className="text-gray-600">Tinggi Badan</div>
                  <div className="font-semibold text-gizi-dark">{child.height} cm</div>
                </div>
              </div>

              {child.status === 'danger' && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <div className="font-medium text-red-800">Perlu Tindakan Segera</div>
                      <div className="text-red-600">Rujuk ke fasilitas kesehatan untuk pemeriksaan lebih lanjut</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredChildren.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">Data tidak ditemukan</h3>
            <p className="text-gray-400">Coba ubah filter atau kata kunci pencarian</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ManageData;
