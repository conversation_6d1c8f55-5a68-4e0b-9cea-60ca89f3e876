
import { useParams, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import DashboardLayout from '../components/DashboardLayout';
import { ArrowLeft, Plus, TrendingUp, Calendar, Weight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';

// Mock data
const mockChild = {
  id: '1',
  name: '<PERSON><PERSON><PERSON>',
  birth_date: '2022-09-15',
  gender: 'female',
  growth_records: [
    { id: '1', record_date: '2024-10-01', weight_kg: 11.5, height_cm: 82.0 },
    { id: '2', record_date: '2024-11-01', weight_kg: 12.0, height_cm: 83.5 },
    { id: '3', record_date: '2024-12-01', weight_kg: 12.5, height_cm: 85.2 },
  ]
};

const ChildDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [child] = useState(mockChild);
  const [isAddDataOpen, setIsAddDataOpen] = useState(false);
  const [newRecord, setNewRecord] = useState({
    weight_kg: '',
    height_cm: '',
    record_date: new Date().toISOString().split('T')[0]
  });

  const calculateAge = (birthDate: string) => {
    const birth = new Date(birthDate);
    const today = new Date();
    const years = today.getFullYear() - birth.getFullYear();
    const months = today.getMonth() - birth.getMonth();
    
    if (years > 0) {
      return `${years} tahun ${months >= 0 ? months : 12 + months} bulan`;
    } else {
      const totalMonths = years * 12 + months;
      return `${totalMonths} bulan`;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const handleAddRecord = () => {
    if (!newRecord.weight_kg || !newRecord.height_cm) {
      toast({
        title: "Data tidak lengkap",
        description: "Mohon isi berat dan tinggi badan",
        variant: "destructive",
      });
      return;
    }

    console.log('Adding growth record:', newRecord);
    toast({
      title: "Data berhasil ditambah",
      description: "Data pertumbuhan telah disimpan",
    });

    setIsAddDataOpen(false);
    setNewRecord({
      weight_kg: '',
      height_cm: '',
      record_date: new Date().toISOString().split('T')[0]
    });
  };

  const getGrowthStatus = (weight: number, height: number) => {
    // Simple mock calculation
    if (weight > 12 && height > 84) return { status: 'normal', text: 'Pertumbuhan Normal', color: 'text-gizi-primary' };
    if (weight > 11 && height > 80) return { status: 'warning', text: 'Perlu Perhatian', color: 'text-gizi-secondary' };
    return { status: 'danger', text: 'Berisiko Stunting', color: 'text-gizi-danger' };
  };

  const latestRecord = child.growth_records[child.growth_records.length - 1];
  const growthStatus = getGrowthStatus(latestRecord.weight_kg, latestRecord.height_cm);

  return (
    <DashboardLayout title={`Detail ${child.name}`}>
      <div className="space-y-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => navigate('/children')}
          className="text-gizi-primary hover:bg-gizi-primary/10"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Daftar Anak
        </Button>

        {/* Child Info */}
        <div className="gizi-card">
          <div className="flex items-center space-x-4 mb-4">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
              child.gender === 'female' 
                ? 'bg-gradient-to-br from-pink-400 to-pink-600' 
                : 'bg-gradient-to-br from-blue-400 to-blue-600'
            }`}>
              <span className="text-2xl text-white font-bold">
                {child.name.charAt(0)}
              </span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gizi-dark">{child.name}</h1>
              <p className="text-gray-600">
                {child.gender === 'female' ? 'Perempuan' : 'Laki-laki'} • {calculateAge(child.birth_date)}
              </p>
              <p className="text-sm text-gray-500">
                Lahir: {formatDate(child.birth_date)}
              </p>
            </div>
          </div>

          {/* Latest Status */}
          <div className="bg-gizi-light rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gizi-dark mb-1">Status Pertumbuhan Terkini</h3>
                <p className={`font-medium ${growthStatus.color}`}>
                  {growthStatus.text}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  BB: {latestRecord.weight_kg} kg • TB: {latestRecord.height_cm} cm
                </p>
              </div>
              <TrendingUp className={`w-8 h-8 ${growthStatus.color.replace('text-', 'text-')}`} />
            </div>
          </div>
        </div>

        {/* Add Growth Data Button */}
        <Dialog open={isAddDataOpen} onOpenChange={setIsAddDataOpen}>
          <DialogTrigger asChild>
            <Button className="w-full gizi-button-primary">
              <Plus className="w-4 h-4 mr-2" />
              Tambah Data Pertumbuhan
            </Button>
          </DialogTrigger>
          <DialogContent className="w-[90%] max-w-md mx-auto">
            <DialogHeader>
              <DialogTitle>Tambah Data Pertumbuhan</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="record_date">Tanggal Pengukuran</Label>
                <Input
                  id="record_date"
                  type="date"
                  value={newRecord.record_date}
                  onChange={(e) => setNewRecord(prev => ({ ...prev, record_date: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="weight">Berat Badan (kg)</Label>
                <Input
                  id="weight"
                  type="number"
                  step="0.1"
                  placeholder="contoh: 12.5"
                  value={newRecord.weight_kg}
                  onChange={(e) => setNewRecord(prev => ({ ...prev, weight_kg: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="height">Tinggi Badan (cm)</Label>
                <Input
                  id="height"
                  type="number"
                  step="0.1"
                  placeholder="contoh: 85.2"
                  value={newRecord.height_cm}
                  onChange={(e) => setNewRecord(prev => ({ ...prev, height_cm: e.target.value }))}
                />
              </div>
              <Button onClick={handleAddRecord} className="w-full gizi-button-primary">
                Simpan Data
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Growth Chart Placeholder */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4">Grafik Pertumbuhan</h2>
          <div className="bg-gradient-to-br from-gizi-light to-white rounded-lg p-8 text-center border-2 border-dashed border-gray-200">
            <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">Grafik pertumbuhan akan ditampilkan di sini</p>
            <p className="text-sm text-gray-400">Menggunakan standar WHO untuk perbandingan</p>
          </div>
        </div>

        {/* Growth Records History */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4">Riwayat Pertumbuhan</h2>
          <div className="space-y-3">
            {child.growth_records.slice().reverse().map((record) => (
              <div key={record.id} className="flex items-center space-x-4 p-3 bg-gizi-light rounded-lg">
                <div className="w-10 h-10 bg-gizi-primary/10 rounded-full flex items-center justify-center">
                  <Weight className="w-5 h-5 text-gizi-primary" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <span className="font-medium text-gizi-dark">
                      {record.weight_kg} kg
                    </span>
                    <span className="font-medium text-gizi-dark">
                      {record.height_cm} cm
                    </span>
                  </div>
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <Calendar className="w-3 h-3" />
                    <span>{formatDate(record.record_date)}</span>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`text-sm font-medium ${getGrowthStatus(record.weight_kg, record.height_cm).color}`}>
                    {getGrowthStatus(record.weight_kg, record.height_cm).text}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2">💡 Tips Pemantauan</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Ukur berat dan tinggi badan secara rutin setiap bulan</li>
            <li>• Konsultasikan dengan petugas kesehatan jika ada kekhawatiran</li>
            <li>• Berikan gizi seimbang dan ASI eksklusif hingga 6 bulan</li>
            <li>• Jaga kebersihan dan sanitasi lingkungan</li>
          </ul>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ChildDetail;
