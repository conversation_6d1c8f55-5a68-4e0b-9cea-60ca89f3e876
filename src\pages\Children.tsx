
import { useState } from 'react';
import { Link } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import { Baby, Plus, Calendar, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';

// Mock data
const mockChildren = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>',
    birth_date: '2022-09-15',
    gender: 'female',
    last_weight: 12.5,
    last_height: 85.2,
    status: 'normal'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    birth_date: '2021-03-22',
    gender: 'male',
    last_weight: 14.2,
    last_height: 92.1,
    status: 'warning'
  }
];

const Children = () => {
  const [children] = useState(mockChildren);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newChild, setNewChild] = useState({
    name: '',
    birth_date: '',
    gender: ''
  });

  const calculateAge = (birthDate: string) => {
    const birth = new Date(birthDate);
    const today = new Date();
    const years = today.getFullYear() - birth.getFullYear();
    const months = today.getMonth() - birth.getMonth();
    
    if (years > 0) {
      return `${years} tahun ${months >= 0 ? months : 12 + months} bulan`;
    } else {
      const totalMonths = years * 12 + months;
      return `${totalMonths} bulan`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'text-gizi-primary';
      case 'warning': return 'text-gizi-secondary';
      case 'danger': return 'text-gizi-danger';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal': return 'Pertumbuhan Normal';
      case 'warning': return 'Perlu Perhatian';
      case 'danger': return 'Berisiko Stunting';
      default: return 'Belum ada data';
    }
  };

  const handleAddChild = () => {
    if (!newChild.name || !newChild.birth_date || !newChild.gender) {
      toast({
        title: "Data tidak lengkap",
        description: "Mohon isi semua data anak",
        variant: "destructive",
      });
      return;
    }

    console.log('Adding child:', newChild);
    toast({
      title: "Berhasil menambah anak",
      description: `Data ${newChild.name} telah disimpan`,
    });

    setIsAddDialogOpen(false);
    setNewChild({ name: '', birth_date: '', gender: '' });
  };

  return (
    <DashboardLayout title="Anak Saya">
      <div className="space-y-6">
        {/* Add Child Button */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <button className="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gizi-primary transition-colors">
              <Plus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600 font-medium">Tambah Anak Baru</p>
            </button>
          </DialogTrigger>
          <DialogContent className="w-[90%] max-w-md mx-auto">
            <DialogHeader>
              <DialogTitle>Tambah Anak Baru</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Nama Anak</Label>
                <Input
                  id="name"
                  value={newChild.name}
                  onChange={(e) => setNewChild(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Masukkan nama anak"
                />
              </div>
              <div>
                <Label htmlFor="birth_date">Tanggal Lahir</Label>
                <Input
                  id="birth_date"
                  type="date"
                  value={newChild.birth_date}
                  onChange={(e) => setNewChild(prev => ({ ...prev, birth_date: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="gender">Jenis Kelamin</Label>
                <Select onValueChange={(value) => setNewChild(prev => ({ ...prev, gender: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis kelamin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Laki-laki</SelectItem>
                    <SelectItem value="female">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={handleAddChild} className="w-full gizi-button-primary">
                Simpan Data Anak
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Children List */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gizi-dark">Daftar Anak ({children.length})</h2>
          
          {children.map((child) => (
            <Link 
              key={child.id} 
              to={`/children/${child.id}`}
              className="gizi-card flex items-center space-x-4 hover:shadow-md transition-shadow"
            >
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                child.gender === 'female' 
                  ? 'bg-gradient-to-br from-pink-400 to-pink-600' 
                  : 'bg-gradient-to-br from-blue-400 to-blue-600'
              }`}>
                <Baby className="w-6 h-6 text-white" />
              </div>
              
              <div className="flex-1">
                <h3 className="font-medium text-gizi-dark">{child.name}</h3>
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <Calendar className="w-3 h-3" />
                  <span>{calculateAge(child.birth_date)}</span>
                </div>
                <div className="flex items-center space-x-2 mt-1">
                  <div className={`w-2 h-2 rounded-full ${
                    child.status === 'normal' ? 'bg-gizi-primary' :
                    child.status === 'warning' ? 'bg-gizi-secondary' : 'bg-gizi-danger'
                  }`}></div>
                  <span className={`text-xs font-medium ${getStatusColor(child.status)}`}>
                    {getStatusText(child.status)}
                  </span>
                </div>
                
                {child.last_weight && (
                  <div className="text-xs text-gray-500 mt-1">
                    BB: {child.last_weight} kg • TB: {child.last_height} cm
                  </div>
                )}
              </div>
              
              <TrendingUp className="w-5 h-5 text-gizi-primary" />
            </Link>
          ))}
        </div>

        {children.length === 0 && (
          <div className="text-center py-12">
            <Baby className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">Belum ada data anak</h3>
            <p className="text-gray-400">Tambahkan data anak untuk mulai memantau pertumbuhan</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Children;
