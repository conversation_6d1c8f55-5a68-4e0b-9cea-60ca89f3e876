
import { useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import { ArrowLeft, Clock, BookOpen, Share } from 'lucide-react';
import { Button } from '@/components/ui/button';
import articlesData from '../data/articles.json';

const ArticleDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const article = articlesData.find(a => a.id === id);

  if (!article) {
    return (
      <DashboardLayout title="Artikel Tidak Ditemukan">
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Artikel yang Anda cari tidak ditemukan</p>
          <Button onClick={() => navigate('/education')} variant="outline">
            Kembali ke Edukasi
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Extended content for demonstration
  const fullContent = `
${article.content}

## Pentingnya Pencegahan Stunting

Stunting merupakan masalah kesehatan masyarakat yang serius di Indonesia, khususnya di Nusa Tenggara Timur. Kondisi ini tidak hanya mempengaruhi pertumbuhan fisik anak, tetapi juga perkembangan kognitif dan produktivitas di masa depan.

### Faktor-faktor Penyebab Stunting

1. **Gizi Kurang**: Kekurangan asupan gizi dalam 1000 hari pertama kehidupan
2. **Infeksi Berulang**: Diare, pneumonia, dan infeksi lainnya
3. **Sanitasi Buruk**: Lingkungan yang tidak bersih
4. **Akses Kesehatan Terbatas**: Kurangnya layanan kesehatan yang memadai

### Langkah-langkah Pencegahan

**1. Perbaikan Gizi**
- Berikan ASI eksklusif hingga 6 bulan
- MP-ASI bergizi seimbang mulai usia 6 bulan
- Manfaatkan pangan lokal yang bergizi tinggi

**2. Perbaikan Sanitasi**
- Cuci tangan dengan sabun
- Gunakan air bersih
- Buang air besar di jamban

**3. Pemantauan Pertumbuhan**
- Kunjungi Posyandu secara rutin
- Pantau berat dan tinggi badan anak
- Konsultasi dengan tenaga kesehatan

### Peran Masyarakat

Pencegahan stunting memerlukan kerja sama dari semua pihak:
- **Keluarga**: Memberikan gizi terbaik untuk anak
- **Kader Posyandu**: Memantau pertumbuhan anak di wilayahnya
- **Pemerintah**: Menyediakan layanan kesehatan dan edukasi
- **Masyarakat**: Mendukung program pencegahan stunting

### Kesimpulan

Dengan upaya bersama dan konsisten, stunting dapat dicegah. Mari manfaatkan kekayaan pangan lokal NTT untuk memberikan nutrisi terbaik bagi generasi masa depan.
  `;

  return (
    <DashboardLayout title="Artikel">
      <div className="space-y-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => navigate('/education')}
          className="text-gizi-primary hover:bg-gizi-primary/10"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Edukasi
        </Button>

        {/* Article Header */}
        <div className="gizi-card">
          <div className="w-full h-48 bg-gradient-to-br from-gizi-primary/20 to-gizi-secondary/20 rounded-lg flex items-center justify-center mb-6">
            <BookOpen className="w-16 h-16 text-gizi-primary" />
          </div>
          
          <div className="flex items-center space-x-2 mb-3">
            <span className="bg-gizi-primary/10 text-gizi-primary text-sm px-3 py-1 rounded-full">
              {article.category}
            </span>
          </div>
          
          <h1 className="text-2xl font-bold text-gizi-dark mb-4">{article.title}</h1>
          
          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{article.read_time}</span>
              </div>
              <span>{formatDate(article.created_at)}</span>
            </div>
            <Button variant="ghost" size="sm" className="text-gizi-primary">
              <Share className="w-4 h-4 mr-1" />
              Bagikan
            </Button>
          </div>
          
          <p className="text-lg text-gray-600 leading-relaxed">{article.summary}</p>
        </div>

        {/* Article Content */}
        <div className="gizi-card">
          <div className="prose prose-gray max-w-none">
            {fullContent.split('\n\n').map((paragraph, index) => {
              if (paragraph.startsWith('##')) {
                return (
                  <h2 key={index} className="text-xl font-bold text-gizi-dark mt-8 mb-4">
                    {paragraph.replace('## ', '')}
                  </h2>
                );
              } else if (paragraph.startsWith('###')) {
                return (
                  <h3 key={index} className="text-lg font-semibold text-gizi-dark mt-6 mb-3">
                    {paragraph.replace('### ', '')}
                  </h3>
                );
              } else if (paragraph.startsWith('**') && paragraph.endsWith('**')) {
                return (
                  <h4 key={index} className="font-semibold text-gizi-dark mt-4 mb-2">
                    {paragraph.replace(/\*\*/g, '')}
                  </h4>
                );
              } else if (paragraph.includes('- ')) {
                const items = paragraph.split('\n').filter(item => item.startsWith('- '));
                return (
                  <ul key={index} className="list-disc list-inside space-y-1 mb-4 text-gray-700">
                    {items.map((item, itemIndex) => (
                      <li key={itemIndex}>{item.replace('- ', '')}</li>
                    ))}
                  </ul>
                );
              } else if (paragraph.includes('. ') && /^\d+\./.test(paragraph.split('\n')[0])) {
                const items = paragraph.split('\n').filter(item => /^\d+\./.test(item));
                return (
                  <ol key={index} className="list-decimal list-inside space-y-1 mb-4 text-gray-700">
                    {items.map((item, itemIndex) => (
                      <li key={itemIndex}>{item.replace(/^\d+\. /, '')}</li>
                    ))}
                  </ol>
                );
              } else if (paragraph.trim()) {
                return (
                  <p key={index} className="text-gray-700 leading-relaxed mb-4">
                    {paragraph}
                  </p>
                );
              }
              return null;
            })}
          </div>
        </div>

        {/* Related Articles */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4">Artikel Terkait</h2>
          <div className="space-y-3">
            {articlesData
              .filter(a => a.id !== article.id && a.category === article.category)
              .slice(0, 3)
              .map(relatedArticle => (
                <button
                  key={relatedArticle.id}
                  onClick={() => navigate(`/education/${relatedArticle.id}`)}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:border-gizi-primary transition-colors"
                >
                  <h3 className="font-medium text-gizi-dark mb-1">{relatedArticle.title}</h3>
                  <p className="text-sm text-gray-600">{relatedArticle.read_time} • {relatedArticle.category}</p>
                </button>
              ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gizi-primary text-white rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold mb-2">Punya Pertanyaan?</h3>
          <p className="mb-4">Konsultasikan dengan tenaga kesehatan terdekat untuk informasi lebih lanjut</p>
          <Button variant="secondary" className="bg-white text-gizi-primary hover:bg-gray-100">
            Cari Faskes Terdekat
          </Button>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ArticleDetail;
