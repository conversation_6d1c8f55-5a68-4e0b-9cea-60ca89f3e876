
import { useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import { ArrowLeft, Clock, Baby, Heart, ChefHat } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import recipesData from '../data/recipes.json';

const RecipeDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const recipe = recipesData.find(r => r.id === id);

  if (!recipe) {
    return (
      <DashboardLayout title="Resep Tidak Ditemukan">
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Resep yang Anda cari tidak ditemukan</p>
          <Button onClick={() => navigate('/recipes')} variant="outline">
            Kembali ke Resep
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Detail Resep">
      <div className="space-y-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => navigate('/recipes')}
          className="text-gizi-primary hover:bg-gizi-primary/10"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Resep
        </Button>

        {/* Recipe Header */}
        <div className="gizi-card">
          <div className="w-full h-48 bg-gradient-to-br from-gizi-primary/20 to-gizi-secondary/20 rounded-lg flex items-center justify-center mb-4">
            <ChefHat className="w-16 h-16 text-gizi-primary" />
          </div>
          
          <h1 className="text-2xl font-bold text-gizi-dark mb-2">{recipe.title}</h1>
          <p className="text-gray-600 mb-4">{recipe.description}</p>
          
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4" />
              <span>{recipe.prep_time}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Baby className="w-4 h-4" />
              <span>{recipe.age_range}</span>
            </div>
          </div>
          
          <div className="mt-4">
            <span className="inline-block bg-gizi-primary/10 text-gizi-primary text-sm px-3 py-1 rounded-full">
              Bahan Utama: {recipe.main_ingredient}
            </span>
          </div>
        </div>

        {/* Ingredients */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4">Bahan-bahan</h2>
          <ul className="space-y-2">
            {recipe.ingredients.map((ingredient, index) => (
              <li key={index} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gizi-primary rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-gray-700">{ingredient}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Instructions */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4">Cara Membuat</h2>
          <div className="space-y-3">
            {recipe.instructions.split('\n').map((step, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gizi-primary text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                  {index + 1}
                </div>
                <p className="text-gray-700 pt-0.5">{step}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Nutritional Benefits */}
        <div className="gizi-card">
          <div className="flex items-center space-x-2 mb-3">
            <Heart className="w-5 h-5 text-red-500" />
            <h2 className="text-lg font-semibold text-gizi-dark">Manfaat Gizi</h2>
          </div>
          <p className="text-gray-700">{recipe.nutritional_benefits}</p>
        </div>

        {/* Safety Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2">⚠️ Tips Keamanan</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Pastikan semua bahan segar dan bersih</li>
            <li>• Masak hingga matang sempurna</li>
            <li>• Sajikan dalam suhu yang tepat untuk bayi</li>
            <li>• Konsultasikan dengan dokter jika ada reaksi alergi</li>
          </ul>
        </div>

        {/* Related Recipes */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4">Resep Serupa</h2>
          <div className="space-y-3">
            {recipesData
              .filter(r => r.id !== recipe.id && r.main_ingredient === recipe.main_ingredient)
              .slice(0, 2)
              .map(relatedRecipe => (
                <button
                  key={relatedRecipe.id}
                  onClick={() => navigate(`/recipes/${relatedRecipe.id}`)}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:border-gizi-primary transition-colors"
                >
                  <h3 className="font-medium text-gizi-dark">{relatedRecipe.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{relatedRecipe.prep_time} • {relatedRecipe.age_range}</p>
                </button>
              ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default RecipeDetail;
