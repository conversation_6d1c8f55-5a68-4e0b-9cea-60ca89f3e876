
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import DashboardLayout from '../components/DashboardLayout';
import { Baby, ChefHat, TrendingUp, Users, AlertTriangle, Calendar, Plus } from 'lucide-react';

const Dashboard = () => {
  const { user } = useAuth();

  if (user?.role === 'cadre') {
    return (
      <DashboardLayout title="Dashboard Kader">
        <div className="space-y-6">
          {/* Stats Overview */}
          <div className="grid grid-cols-2 gap-4">
            <div className="gizi-card text-center">
              <div className="text-2xl font-bold text-gizi-primary mb-1">42</div>
              <div className="text-sm text-gray-600">Total Anak</div>
            </div>
            <div className="gizi-card text-center">
              <div className="text-2xl font-bold text-gizi-danger mb-1">3</div>
              <div className="text-sm text-gray-600"><PERSON><PERSON>ian</div>
            </div>
          </div>

          {/* At-Risk Children Alert */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-800 mb-1">Anak Berisiko Stunting</h3>
                <p className="text-sm text-red-600 mb-3">
                  3 anak memerlukan perhatian khusus
                </p>
                <Link 
                  to="/manage-data"
                  className="text-sm bg-red-100 text-red-700 px-3 py-1 rounded-lg hover:bg-red-200 transition-colors"
                >
                  Lihat Detail
                </Link>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gizi-dark">Aksi Cepat</h2>
            
            <Link to="/manage-data" className="gizi-card flex items-center space-x-4 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-gizi-primary/10 rounded-lg flex items-center justify-center">
                <Plus className="w-6 h-6 text-gizi-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gizi-dark">Input Data Massal</h3>
                <p className="text-sm text-gray-600">Tambah data pertumbuhan anak</p>
              </div>
            </Link>

            <Link to="/send-info" className="gizi-card flex items-center space-x-4 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-gizi-secondary/10 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-gizi-secondary" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gizi-dark">Kirim Informasi</h3>
                <p className="text-sm text-gray-600">Broadcast ke orang tua</p>
              </div>
            </Link>
          </div>

          {/* Next Posyandu Schedule */}
          <div className="gizi-card">
            <div className="flex items-center space-x-3 mb-3">
              <Calendar className="w-5 h-5 text-gizi-primary" />
              <h3 className="font-medium text-gizi-dark">Jadwal Posyandu</h3>
            </div>
            <div className="bg-gizi-light rounded-lg p-3">
              <p className="font-medium text-gizi-dark">Minggu Ini</p>
              <p className="text-sm text-gray-600">Sabtu, 21 Desember 2024</p>
              <p className="text-sm text-gray-600">08:00 - 12:00 WIB</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Parent Dashboard
  return (
    <DashboardLayout title="Dashboard">
      <div className="space-y-6">
        {/* Welcome Message */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-2">
            Selamat datang, {user?.full_name}! 👋
          </h2>
          <p className="text-gray-600">
            Mari pantau tumbuh kembang anak dengan GiziLokal
          </p>
        </div>

        {/* Children Summary */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gizi-dark">Anak Saya</h2>
            <Link to="/children" className="text-gizi-primary font-medium text-sm">
              Lihat Semua
            </Link>
          </div>

          {/* Mock child card */}
          <Link to="/children/1" className="gizi-card flex items-center space-x-4 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
              <Baby className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-gizi-dark">Aisyah</h3>
              <p className="text-sm text-gray-600">2 tahun 3 bulan</p>
              <div className="flex items-center space-x-2 mt-1">
                <div className="w-2 h-2 bg-gizi-primary rounded-full"></div>
                <span className="text-xs text-gizi-primary font-medium">Pertumbuhan Normal</span>
              </div>
            </div>
            <TrendingUp className="w-5 h-5 text-gizi-primary" />
          </Link>

          <Link to="/children" className="block w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gizi-primary transition-colors">
            <Plus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 font-medium">Tambah Anak</p>
          </Link>
        </div>

        {/* Quick Access */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gizi-dark">Akses Cepat</h2>
          
          <div className="grid grid-cols-2 gap-4">
            <Link to="/recipes" className="gizi-card text-center hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-gizi-secondary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                <ChefHat className="w-6 h-6 text-gizi-secondary" />
              </div>
              <h3 className="font-medium text-gizi-dark mb-1">Resep Lokal</h3>
              <p className="text-xs text-gray-600">MP-ASI bergizi</p>
            </Link>

            <Link to="/education" className="gizi-card text-center hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-gizi-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Baby className="w-6 h-6 text-gizi-primary" />
              </div>
              <h3 className="font-medium text-gizi-dark mb-1">Edukasi</h3>
              <p className="text-xs text-gray-600">Tips kesehatan</p>
            </Link>
          </div>
        </div>

        {/* Next Posyandu Reminder */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Calendar className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-800 mb-1">Jadwal Posyandu</h3>
              <p className="text-sm text-blue-600">
                Jangan lupa datang ke Posyandu Sabtu ini (21 Des) untuk pemeriksaan rutin anak.
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
