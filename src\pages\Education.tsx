
import { useState } from 'react';
import { Link } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import { BookOpen, Clock, Search, Tag } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import articlesData from '../data/articles.json';

const Education = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');

  const categories = ['Stunting', 'Tumbuh Kembang', 'Gizi', 'Superfood', 'Monitoring', 'Kesehatan'];

  const filteredArticles = articlesData.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.summary.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || article.category === filterCategory;
    
    return matchesSearch && matchesCategory;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  return (
    <DashboardLayout title="Edukasi Gizi">
      <div className="space-y-8">
        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Cari artikel edukasi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger>
                <div className="flex items-center space-x-2">
                  <Tag className="w-4 h-4" />
                  <SelectValue placeholder="Pilih Kategori" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Kategori</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Featured Article */}
        {filteredArticles.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
              <div className="w-full h-48 bg-gradient-to-br from-gizi-primary/20 to-gizi-secondary/20 rounded-lg flex items-center justify-center">
                <BookOpen className="w-16 h-16 text-gizi-primary" />
              </div>
              
              <div>
                <div className="flex items-center space-x-2 mb-3">
                  <span className="bg-gizi-primary/10 text-gizi-primary text-sm px-3 py-1 rounded-full">
                    Artikel Unggulan
                  </span>
                  <span className="bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded-full">
                    {filteredArticles[0].category}
                  </span>
                </div>
                
                <Link to={`/education/${filteredArticles[0].id}`}>
                  <h2 className="text-xl font-bold text-gizi-dark mb-3 hover:text-gizi-primary transition-colors">
                    {filteredArticles[0].title}
                  </h2>
                </Link>
                
                <p className="text-gray-600 mb-4">{filteredArticles[0].summary}</p>
                
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{filteredArticles[0].read_time}</span>
                  </div>
                  <span>{formatDate(filteredArticles[0].created_at)}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Articles Grid */}
        <div>
          <h2 className="text-xl font-semibold text-gizi-dark mb-6">
            Artikel Edukasi ({filteredArticles.length})
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredArticles.slice(1).map((article) => (
              <Link
                key={article.id}
                to={`/education/${article.id}`}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow block"
              >
                <div className="w-full h-32 bg-gradient-to-br from-gizi-primary/20 to-gizi-secondary/20 rounded-lg flex items-center justify-center mb-4">
                  <BookOpen className="w-8 h-8 text-gizi-primary" />
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                    {article.category}
                  </span>
                </div>
                
                <h3 className="font-semibold text-gizi-dark mb-2 line-clamp-2">
                  {article.title}
                </h3>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-3">
                  {article.summary}
                </p>
                
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{article.read_time}</span>
                  </div>
                  <span>{formatDate(article.created_at)}</span>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Empty State */}
        {filteredArticles.length === 0 && (
          <div className="text-center py-16">
            <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">Artikel tidak ditemukan</h3>
            <p className="text-gray-400">Coba ubah kata kunci pencarian atau kategori</p>
          </div>
        )}

        {/* Tips Card */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="font-semibold text-green-800 mb-2">💡 Tips Belajar</h3>
          <p className="text-green-700">
            Baca artikel secara rutin untuk memperdalam pengetahuan tentang gizi dan tumbuh kembang anak. 
            Jangan ragu untuk bertanya kepada tenaga kesehatan jika ada yang belum dipahami.
          </p>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Education;
