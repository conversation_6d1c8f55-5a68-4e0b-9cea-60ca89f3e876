
import { useAuth } from '../contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import Landing from './Landing';
import LoadingSpinner from '../components/LoadingSpinner';

const Index = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  return <Landing />;
};

export default Index;
