
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 122 39% 49%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    /* Custom GiziLokal colors */
    --gizi-primary: 122 39% 49%;
    --gizi-secondary: 45 100% 55%;
    --gizi-light: 210 20% 98%;
    --gizi-dark: 210 24% 16%;
    --gizi-danger: 0 84% 60%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-poppins;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .gizi-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-100 p-6 transition-all duration-200 hover:shadow-md;
  }

  .gizi-button-primary {
    @apply bg-gizi-primary text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:bg-green-600 active:scale-95 inline-block text-center;
  }

  .gizi-button-secondary {
    @apply bg-gizi-secondary text-gizi-dark px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:bg-yellow-500 active:scale-95 inline-block text-center;
  }

  .gizi-input {
    @apply w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-gizi-primary focus:border-gizi-primary transition-all duration-200;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
