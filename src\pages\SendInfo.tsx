
import DashboardLayout from '../components/DashboardLayout';
import { useState } from 'react';
import { Send, Users, MessageCircle, Calendar, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';

const SendInfo = () => {
  const [message, setMessage] = useState('');
  const [recipient, setRecipient] = useState('all');
  const [messageHistory] = useState([
    {
      id: '1',
      message: 'Jangan lupa datang ke Posyandu Sabtu ini (21 Desember) jam 08:00 untuk pemeriksaan rutin anak.',
      sent_to: 'Semua Orang Tua',
      sent_at: '2024-12-18T10:30:00',
      status: 'sent'
    },
    {
      id: '2',
      message: 'Reminder: <PERSON><PERSON> buku KIA dan KMS saat datang ke Posyandu.',
      sent_to: 'Se<PERSON><PERSON> Orang Tu<PERSON>',
      sent_at: '2024-12-15T14:20:00',
      status: 'sent'
    },
    {
      id: '3',
      message: 'Info: Tersedia vaksin DPT di Posyandu bulan ini. Pastikan anak mendapat imunisasi sesuai jadwal.',
      sent_to: 'Orang Tua dengan Anak 2-24 bulan',
      sent_at: '2024-12-10T09:15:00',
      status: 'sent'
    }
  ]);

  const predefinedMessages = [
    'Jangan lupa datang ke Posyandu Sabtu ini untuk pemeriksaan rutin anak.',
    'Reminder: Bawa buku KIA dan KMS saat datang ke Posyandu.',
    'Info: Tersedia vaksin lengkap di Posyandu bulan ini.',
    'Pentingnya memberikan ASI eksklusif hingga 6 bulan untuk mencegah stunting.',
    'Pastikan anak mengonsumsi makanan bergizi dari bahan lokal NTT.'
  ];

  const recipientOptions = [
    { value: 'all', label: 'Semua Orang Tua' },
    { value: 'at_risk', label: 'Orang Tua Anak Berisiko' },
    { value: 'infants', label: 'Orang Tua Bayi 0-12 bulan' },
    { value: 'toddlers', label: 'Orang Tua Balita 1-5 tahun' }
  ];

  const handleSendMessage = () => {
    if (!message.trim()) {
      toast({
        title: "Pesan kosong",
        description: "Mohon tulis pesan yang akan dikirim",
        variant: "destructive",
      });
      return;
    }

    console.log('Sending message:', { message, recipient });
    toast({
      title: "Pesan berhasil dikirim!",
      description: `Pesan telah dikirim ke ${recipientOptions.find(r => r.value === recipient)?.label}`,
    });

    setMessage('');
    setRecipient('all');
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRecipientCount = (recipientType: string) => {
    switch (recipientType) {
      case 'all': return 45;
      case 'at_risk': return 8;
      case 'infants': return 12;
      case 'toddlers': return 33;
      default: return 0;
    }
  };

  return (
    <DashboardLayout title="Kirim Informasi">
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="gizi-card text-center">
            <div className="text-2xl font-bold text-gizi-primary mb-1">45</div>
            <div className="text-sm text-gray-600">Total Orang Tua</div>
          </div>
          <div className="gizi-card text-center">
            <div className="text-2xl font-bold text-blue-500 mb-1">{messageHistory.length}</div>
            <div className="text-sm text-gray-600">Pesan Terkirim</div>
          </div>
        </div>

        {/* Send New Message */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4 flex items-center">
            <Send className="w-5 h-5 mr-2" />
            Kirim Pesan Baru
          </h2>

          <div className="space-y-4">
            {/* Recipient Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Penerima
              </label>
              <Select value={recipient} onValueChange={setRecipient}>
                <SelectTrigger>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4" />
                    <SelectValue />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {recipientOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label} ({getRecipientCount(option.value)} orang)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Message Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pesan
              </label>
              <Textarea
                placeholder="Tulis pesan informasi untuk orang tua..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={4}
                className="resize-none"
                maxLength={300}
              />
              <div className="text-right text-sm text-gray-500 mt-1">
                {message.length}/300 karakter
              </div>
            </div>

            {/* Predefined Messages */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Template Pesan
              </label>
              <div className="space-y-2">
                {predefinedMessages.map((template, index) => (
                  <button
                    key={index}
                    onClick={() => setMessage(template)}
                    className="w-full text-left p-3 text-sm border border-gray-200 rounded-lg hover:border-gizi-primary hover:bg-gizi-primary/5 transition-colors"
                  >
                    {template}
                  </button>
                ))}
              </div>
            </div>

            {/* Send Button */}
            <Button 
              onClick={handleSendMessage} 
              className="w-full gizi-button-primary"
              disabled={!message.trim()}
            >
              <Send className="w-4 h-4 mr-2" />
              Kirim Pesan
            </Button>
          </div>
        </div>

        {/* Message History */}
        <div className="gizi-card">
          <h2 className="text-lg font-semibold text-gizi-dark mb-4 flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Riwayat Pesan
          </h2>

          <div className="space-y-4">
            {messageHistory.map((msg) => (
              <div key={msg.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                    <span className="text-sm font-medium text-gray-700">Terkirim</span>
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Calendar className="w-3 h-3" />
                    <span>{formatDateTime(msg.sent_at)}</span>
                  </div>
                </div>
                
                <p className="text-gray-800 mb-3 leading-relaxed">{msg.message}</p>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    Dikirim ke: <span className="font-medium">{msg.sent_to}</span>
                  </span>
                  <span className="text-green-600 font-medium">
                    ✓ Berhasil
                  </span>
                </div>
              </div>
            ))}
          </div>

          {messageHistory.length === 0 && (
            <div className="text-center py-8">
              <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">Belum ada pesan yang dikirim</p>
            </div>
          )}
        </div>

        {/* Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2">💡 Tips Mengirim Pesan</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Gunakan bahasa yang mudah dipahami</li>
            <li>• Sertakan informasi yang jelas dan spesifik</li>
            <li>• Kirim pengingat untuk jadwal Posyandu</li>
            <li>• Berikan edukasi gizi dan kesehatan secara rutin</li>
          </ul>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default SendInfo;
