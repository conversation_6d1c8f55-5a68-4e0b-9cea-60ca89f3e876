
import { Link } from 'react-router-dom';
import { Home } from 'lucide-react';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gizi-light to-white flex items-center justify-center px-4">
      <div className="text-center max-w-md">
        <div className="mb-8">
          <h1 className="text-8xl font-bold text-gizi-primary mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gizi-dark mb-2">
            Halaman Tidak Ditemukan
          </h2>
          <p className="text-gray-600">
            <PERSON><PERSON>, halaman yang Anda cari tidak dapat ditemukan.
          </p>
        </div>
        
        <Link 
          to="/" 
          className="gizi-button-primary inline-flex items-center space-x-2"
        >
          <Home className="w-5 h-5" />
          <span>Kembali ke Beranda</span>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
